package de.fellows.ems.pcb.impl.matcher.filecontent

import de.fellows.ems.pcb.impl.matcher.Confidence._
import de.fellows.ems.pcb.impl.matcher.{FileMatch, SingleFileMatcher}
import de.fellows.ems.pcb.model.LayerConstants
import de.fellows.ems.pcb.model.LayerConstants.Mime
import de.fellows.utils.FilePath
import de.fellows.utils.communication.ServiceDefinition

import java.nio.file.Files
import scala.collection.mutable

class DrillFileContentMatcher(implicit serviceDefinition: ServiceDefinition) extends SingleFileMatcher(false) {
  override def id: String = "drill-content-matcher"

  val service = serviceDefinition.name

  val confidence = VeryHighConfidence

  override def mime: Option[Seq[String]] = None

  def createMatches(f: FilePath, features: mutable.Map[String, Seq[String]]): Seq[FileMatch] = {
    val name = f.filename.toLowerCase

    val t =
      if (name.contains("npth") || name.contains("ndk")) {
        Some(false)
      } else if (name.contains("pth") || name.contains("dk")) {
        Some(true)
      } else {
        None
      }

    val plated = features.get("filefunction").map { ff =>
      ff.contains("nonplated")
    }

    val ffRange = features.get("filefunction").map { ff =>
      if (ff.length >= 3) {
        (Some(ff(1).toInt), Some(ff(2).toInt))
      } else {
        (None, None)
      }
    }

    val nameRange = DrillFileContentMatcher.extractRangeFromName(name);

    val range = ffRange.orElse(nameRange)

    val dt = (plated.orElse(t).map {
      case true  => LayerConstants.PH_DRILL
      case false => LayerConstants.NPH_DRILL
    }).getOrElse(LayerConstants.DRILL)

    Seq(createMatch(dt, Mime.drill, LayerConstants.Categories.mechanical).copy(
      from = range.flatMap(_._1),
      to = range.flatMap(_._2)
    ))
  }

  override def matchFile(filename: String, file: FilePath, mime: Option[String]): Seq[FileMatch] = {
    var found_M48: Boolean     = false
    var found_M30: Boolean     = false
    var found_percent: Boolean = false
    var found_T: Boolean       = false
    var found_X: Boolean       = false
    var found_Y: Boolean       = false
    var found_M00: Int         = 0
    var end_comments: Boolean  = false

    val fileFunctions: mutable.Map[String, Seq[String]] = mutable.Map()

    Files.readAllLines(file.toJavaPath).forEach { l =>
      found_M30 = false // set to false as m30 means end of file.
      val lowerLine = l.toLowerCase()

      if (lowerLine.trim.startsWith("m48")) {
        found_M48 = true
      }
      if (lowerLine.trim.startsWith("m30")) {
        found_M30 = true
      }
      if (lowerLine.trim.startsWith("m00")) {
        found_M00 += 1
      }
      if (l.trim.startsWith("%")) {
        found_percent = true
      }
      if (DrillFileContentMatcher.T_PATTERN.findFirstMatchIn(l).nonEmpty) {
        found_T = true
      }
      if (DrillFileContentMatcher.X_PATTERN.findFirstMatchIn(l).nonEmpty) {
        found_X = true
      }
      if (DrillFileContentMatcher.Y_PATTERN.findFirstMatchIn(l).nonEmpty) {
        found_Y = true
      }

      // TF.FileFunction,Plated,1,2,PTH
      val tfFileFunction = "tf."
      if (lowerLine.contains(tfFileFunction)) {
        val functions = lowerLine.substring(lowerLine.indexOf(tfFileFunction) + tfFileFunction.length).split(",")
        fileFunctions += functions.head -> functions.tail
      }
    }

    if (((found_X || found_Y) && found_T) && (found_M48 || (found_percent && found_M30))) {
      createMatches(file, fileFunctions)
    } else if (found_M48 && found_T && found_percent && found_M30) {
      createMatches(file, fileFunctions)
    } else if (!found_T && found_M30 && found_M00 >= 1 && (found_X || found_Y)) {
      // extremely weird allegro format
      createMatches(file, fileFunctions)
    } else if (found_M30 && (found_X || found_Y)) {
      // lower confidence
      createMatches(file, fileFunctions)
        .map(_.copy(confidence = MediumLowConfidence))
    } else {
      Seq()
    }
  }
}

object DrillFileContentMatcher {
  val T_PATTERN = "T[0-9]+".r
  val X_PATTERN = "X[0-9]+".r
  val Y_PATTERN = "Y[0-9]+".r

  val drillRange = "l?([0-9]+|bottom|top)(-|_)l?([0-9]+|bottom|top)".r

  def extractRangeFromName(name: String) =
    None
  //    (drillRange.findFirstMatchIn(name).map{ m =>
  //      val from = m.group(1)
  //      val to = m.group(3)
  //
  //      val fromVal =
  //        if (!from.contains("bottom") && !from.contains("top")) {
  //          Some(from.toInt)
  //        } else {
  //          None
  //        }
  //      val toVal =
  //        if (!to.contains("bottom") && !to.contains("top")) {
  //          Some(to.toInt)
  //        } else {
  //          None
  //        }
  //
  //      (fromVal, toVal)
  //    })

}
